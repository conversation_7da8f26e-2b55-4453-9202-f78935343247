import os
import requests
import base64
import zlib

def plantuml_to_image(puml_file, output_file):
    with open(puml_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 压缩并编码内容
    compressed = zlib.compress(content.encode('utf-8'))
    encoded = base64.b64encode(compressed).decode('utf-8')

    # 替换某些字符以符合 PlantUML 服务器的要求
    encoded = encoded.replace('+', '-').replace('/', '_')

    # 构建 URL
    url = f"http://www.plantuml.com/plantuml/png/{encoded}"

    # 下载图片
    response = requests.get(url)

    if response.status_code == 200:
        with open(output_file, 'wb') as f:
            f.write(response.content)
        print(f"图片已保存到 {output_file}")
    else:
        print(f"下载失败，状态码: {response.status_code}")

if __name__ == "__main__":
    # 生成功能结构图
    puml_file = "功能结构图.puml"
    output_file = "images/功能结构图.png"

    # 确保 images 目录存在
    os.makedirs("images", exist_ok=True)

    if os.path.exists(puml_file):
        plantuml_to_image(puml_file, output_file)

    # 生成E-R图
    er_puml_file = "E-R图.puml"
    er_output_file = "images/E-R图.png"

    if os.path.exists(er_puml_file):
        plantuml_to_image(er_puml_file, er_output_file)
