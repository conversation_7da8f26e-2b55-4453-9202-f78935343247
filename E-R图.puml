@startuml 物业管理系统E-R图

!define ENTITY class
!define RELATIONSHIP note

skinparam class {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    ArrowColor DarkBlue
}

skinparam note {
    BackgroundColor LightYellow
    BorderColor Orange
}

' 实体定义
ENTITY Owner {
    + id : INT <<PK>>
    --
    name : VA<PERSON>HA<PERSON>(50) <<UK>>
    gender : VARCHAR(10)
    house_number : VARCHAR(50)
    phone : VARCHAR(20)
    id_number : VARCHAR(50)
    remark : VARCHAR(200)
    emergency_contact : VARCHAR(50)
    emergency_phone : VARCHAR(20)
    create_time : TIMESTAMP
    update_time : TIMESTAMP
}

ENTITY UserAccount {
    + id : INT <<PK>>
    --
    user_name : VA<PERSON><PERSON><PERSON>(50) <<UK>>
    password : VARCHAR(50)
    + owner_id : INT <<FK>>
    create_time : TIMESTAMP
    update_time : TIMESTAMP
}

ENTITY AdminAccount {
    + id : INT <<PK>>
    --
    user_name : <PERSON><PERSON><PERSON><PERSON>(50) <<UK>>
    password : VARCHAR(50)
    create_time : TIMESTAMP
    update_time : TIMES<PERSON>MP
}

ENTITY RepairRequest {
    + id : INT <<PK>>
    --
    name : VA<PERSON>HAR(50)
    house_number : VARCHAR(50)
    repair_content : TEXT
    status : VARCHAR(20)
    + owner_id : INT <<FK>>
    create_time : TIMESTAMP
    update_time : TIMESTAMP
}

ENTITY FeeType {
    + id : INT <<PK>>
    --
    name : VARCHAR(50) <<UK>>
    description : VARCHAR(200)
    unit_price : DECIMAL(10,2)
    billing_cycle : VARCHAR(20)
    calculation_method : VARCHAR(20)
    is_active : TINYINT(1)
    create_time : TIMESTAMP
    update_time : TIMESTAMP
}

ENTITY PaymentBill {
    + id : INT <<PK>>
    --
    bill_no : VARCHAR(50) <<UK>>
    + owner_id : INT <<FK>>
    + fee_type_id : INT <<FK>>
    billing_period : VARCHAR(50)
    amount : DECIMAL(10,2)
    paid_amount : DECIMAL(10,2)
    status : VARCHAR(20)
    due_date : DATE
    remark : VARCHAR(200)
    create_time : TIMESTAMP
    update_time : TIMESTAMP
}

ENTITY PaymentRecord {
    + id : INT <<PK>>
    --
    record_no : VARCHAR(50) <<UK>>
    + bill_id : INT <<FK>>
    + owner_id : INT <<FK>>
    payment_amount : DECIMAL(10,2)
    payment_method : VARCHAR(20)
    payment_time : DATETIME
    operator : VARCHAR(50)
    receipt_no : VARCHAR(50)
    remark : VARCHAR(200)
    create_time : TIMESTAMP
    update_time : TIMESTAMP
}

' 关系定义
Owner ||--|| UserAccount : "拥有"
Owner ||--o{ RepairRequest : "提交"
Owner ||--o{ PaymentBill : "产生"
FeeType ||--o{ PaymentBill : "分类"
PaymentBill ||--o{ PaymentRecord : "记录"
Owner ||--o{ PaymentRecord : "缴费"

' 关系说明
RELATIONSHIP rel1 as "1:1关系\n级联删除"
RELATIONSHIP rel2 as "1:N关系\n级联删除"
RELATIONSHIP rel3 as "1:N关系\n级联删除"
RELATIONSHIP rel4 as "1:N关系\n限制删除"
RELATIONSHIP rel5 as "1:N关系\n限制删除"
RELATIONSHIP rel6 as "1:N关系\n级联删除"

rel1 .. Owner
rel1 .. UserAccount

rel2 .. Owner
rel2 .. RepairRequest

rel3 .. Owner
rel3 .. PaymentBill

rel4 .. FeeType
rel4 .. PaymentBill

rel5 .. PaymentBill
rel5 .. PaymentRecord

rel6 .. Owner
rel6 .. PaymentRecord

@enduml
